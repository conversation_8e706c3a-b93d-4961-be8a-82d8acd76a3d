import os
from collections import Counter

def count_classes_in_balanced_dataset():
    """Count instances per class in the balanced dataset."""
    
    print("=== IMAGES PER CLASS IN BALANCED DATASET ===\n")
    
    # Load class names
    try:
        import yaml
        with open('data.yaml', 'r') as f:
            config = yaml.safe_load(f)
        class_names = config.get('names', [])
    except:
        class_names = []
    
    # Count instances in each split
    splits = ['train', 'valid', 'test']
    all_counts = Counter()
    split_counts = {}
    
    for split in splits:
        labels_dir = os.path.join('balanced_dataset', split, 'labels')
        if not os.path.exists(labels_dir):
            continue
            
        class_counts = Counter()
        for filename in os.listdir(labels_dir):
            if filename.endswith('.txt'):
                filepath = os.path.join(labels_dir, filename)
                try:
                    with open(filepath, 'r') as f:
                        for line in f:
                            if line.strip():
                                class_id = int(line.split()[0])
                                class_counts[class_id] += 1
                except:
                    continue
        
        split_counts[split] = class_counts
        all_counts.update(class_counts)
    
    # Display results
    print(f"Found {len(all_counts)} classes with data")
    print(f"Total instances: {sum(all_counts.values())}")
    print()
    
    # Show each class
    print(f"{'Class':<5} {'Plant Name':<40} {'Train':<6} {'Valid':<6} {'Test':<5} {'Total':<6}")
    print("=" * 75)
    
    for class_id in sorted(all_counts.keys()):
        # Get class name
        if class_id < len(class_names):
            class_name = class_names[class_id]
        else:
            class_name = f"Unknown_Class_{class_id}"
        
        # Truncate long names
        display_name = class_name[:39] if len(class_name) > 39 else class_name
        
        # Get counts for each split
        train_count = split_counts.get('train', {}).get(class_id, 0)
        valid_count = split_counts.get('valid', {}).get(class_id, 0)
        test_count = split_counts.get('test', {}).get(class_id, 0)
        total_count = train_count + valid_count + test_count
        
        print(f"{class_id:<5} {display_name:<40} {train_count:<6} {valid_count:<6} {test_count:<5} {total_count:<6}")
    
    # Summary statistics
    counts = list(all_counts.values())
    counts.sort()
    
    print("=" * 75)
    print(f"SUMMARY:")
    print(f"  Min images per class: {min(counts)}")
    print(f"  Max images per class: {max(counts)}")
    print(f"  Average images per class: {sum(counts) / len(counts):.1f}")
    print(f"  Median images per class: {counts[len(counts)//2]}")
    
    # Show distribution
    ranges = [(0, 30), (30, 50), (50, 70), (70, 100), (100, 999)]
    print(f"\nDistribution:")
    for min_val, max_val in ranges:
        count = len([c for c in counts if min_val <= c < max_val])
        if count > 0:
            print(f"  {min_val}-{max_val-1} images: {count} classes")

def main():
    count_classes_in_balanced_dataset()

if __name__ == "__main__":
    main()

import os
from collections import Counter
import yaml

def load_class_names():
    """Load class names from data.yaml"""
    try:
        with open('data.yaml', 'r') as f:
            config = yaml.safe_load(f)
        return config.get('names', [])
    except:
        return []

def analyze_balanced_dataset():
    """Analyze the balanced dataset to show exact counts per class."""
    
    print("=== DETAILED CLASS ANALYSIS - BALANCED DATASET ===\n")
    
    # Load class names
    class_names = load_class_names()
    
    # Analyze each split
    splits = ['train', 'valid', 'test']
    all_class_counts = Counter()
    split_class_counts = {}
    
    for split in splits:
        labels_dir = os.path.join('balanced_dataset', split, 'labels')
        
        if not os.path.exists(labels_dir):
            print(f"❌ {split} directory not found")
            continue
        
        print(f"📊 Analyzing {split} split...")
        class_counts = Counter()
        
        for filename in os.listdir(labels_dir):
            if filename.endswith('.txt'):
                filepath = os.path.join(labels_dir, filename)
                try:
                    with open(filepath, 'r') as f:
                        for line in f:
                            if line.strip():
                                class_id = int(line.split()[0])
                                class_counts[class_id] += 1
                except Exception as e:
                    print(f"Error reading {filename}: {e}")
        
        split_class_counts[split] = class_counts
        all_class_counts.update(class_counts)
        
        print(f"   {split}: {len(class_counts)} classes, {sum(class_counts.values())} instances")
    
    print(f"\n📈 Total: {len(all_class_counts)} classes, {sum(all_class_counts.values())} instances")
    
    # Show detailed breakdown
    print(f"\n{'='*80}")
    print(f"{'Class':<5} {'Name':<35} {'Train':<7} {'Valid':<7} {'Test':<6} {'Total':<7}")
    print(f"{'='*80}")
    
    total_train = 0
    total_valid = 0
    total_test = 0
    total_all = 0
    
    # Sort by class ID
    for class_id in sorted(all_class_counts.keys()):
        class_name = class_names[class_id] if class_id < len(class_names) else f"Class_{class_id}"
        
        train_count = split_class_counts.get('train', {}).get(class_id, 0)
        valid_count = split_class_counts.get('valid', {}).get(class_id, 0)
        test_count = split_class_counts.get('test', {}).get(class_id, 0)
        total_count = train_count + valid_count + test_count
        
        # Truncate long names
        display_name = class_name[:34] if len(class_name) > 34 else class_name
        
        print(f"{class_id:<5} {display_name:<35} {train_count:<7} {valid_count:<7} {test_count:<6} {total_count:<7}")
        
        total_train += train_count
        total_valid += valid_count
        total_test += test_count
        total_all += total_count
    
    print(f"{'='*80}")
    print(f"{'TOTAL':<5} {'':<35} {total_train:<7} {total_valid:<7} {total_test:<6} {total_all:<7}")
    print(f"{'='*80}")
    
    # Statistics
    counts = list(all_class_counts.values())
    counts.sort()
    
    print(f"\n📊 STATISTICS:")
    print(f"   Classes with data: {len(all_class_counts)}")
    print(f"   Min instances per class: {min(counts) if counts else 0}")
    print(f"   Max instances per class: {max(counts) if counts else 0}")
    print(f"   Average instances per class: {sum(counts) / len(counts):.1f}" if counts else "0")
    print(f"   Median instances per class: {counts[len(counts)//2] if counts else 0}")
    
    # Show distribution
    ranges = [
        (0, 25, "Very low"),
        (25, 50, "Low"), 
        (50, 75, "Medium"),
        (75, 100, "High"),
        (100, float('inf'), "Very high")
    ]
    
    print(f"\n📈 DISTRIBUTION:")
    for min_r, max_r, label in ranges:
        count = len([c for c in counts if min_r <= c < max_r])
        if count > 0:
            max_str = '∞' if max_r == float('inf') else str(max_r)
            print(f"   {label:10s} ({min_r:2d}-{max_str:>3s}): {count:3d} classes")
    
    # Show classes with unusual counts
    avg_count = sum(counts) / len(counts) if counts else 0
    
    low_classes = [(class_id, count) for class_id, count in all_class_counts.items() if count < avg_count * 0.5]
    high_classes = [(class_id, count) for class_id, count in all_class_counts.items() if count > avg_count * 1.5]
    
    if low_classes:
        print(f"\n⚠️  CLASSES WITH LOW COUNTS (< {avg_count * 0.5:.1f}):")
        for class_id, count in sorted(low_classes, key=lambda x: x[1])[:10]:
            class_name = class_names[class_id] if class_id < len(class_names) else f"Class_{class_id}"
            print(f"   Class {class_id:3d} ({class_name[:30]:30s}): {count:3d} instances")
        if len(low_classes) > 10:
            print(f"   ... and {len(low_classes) - 10} more")
    
    if high_classes:
        print(f"\n📈 CLASSES WITH HIGH COUNTS (> {avg_count * 1.5:.1f}):")
        for class_id, count in sorted(high_classes, key=lambda x: x[1], reverse=True)[:10]:
            class_name = class_names[class_id] if class_id < len(class_names) else f"Class_{class_id}"
            print(f"   Class {class_id:3d} ({class_name[:30]:30s}): {count:3d} instances")
        if len(high_classes) > 10:
            print(f"   ... and {len(high_classes) - 10} more")

def analyze_original_vs_balanced():
    """Compare original vs balanced dataset."""
    print(f"\n{'='*80}")
    print("ORIGINAL vs BALANCED COMPARISON")
    print(f"{'='*80}")
    
    # Analyze original dataset
    original_counts = Counter()
    splits = ['train', 'valid', 'test']
    
    for split in splits:
        labels_dir = os.path.join(split, 'labels')
        if os.path.exists(labels_dir):
            for filename in os.listdir(labels_dir):
                if filename.endswith('.txt'):
                    filepath = os.path.join(labels_dir, filename)
                    try:
                        with open(filepath, 'r') as f:
                            for line in f:
                                if line.strip():
                                    class_id = int(line.split()[0])
                                    original_counts[class_id] += 1
                    except:
                        continue
    
    # Analyze balanced dataset
    balanced_counts = Counter()
    for split in splits:
        labels_dir = os.path.join('balanced_dataset', split, 'labels')
        if os.path.exists(labels_dir):
            for filename in os.listdir(labels_dir):
                if filename.endswith('.txt'):
                    filepath = os.path.join(labels_dir, filename)
                    try:
                        with open(filepath, 'r') as f:
                            for line in f:
                                if line.strip():
                                    class_id = int(line.split()[0])
                                    balanced_counts[class_id] += 1
                    except:
                        continue
    
    print(f"Original dataset: {sum(original_counts.values())} instances")
    print(f"Balanced dataset: {sum(balanced_counts.values())} instances")
    print(f"Size reduction: {((sum(original_counts.values()) - sum(balanced_counts.values())) / sum(original_counts.values()) * 100):.1f}%")
    
    # Show some examples
    class_names = load_class_names()
    print(f"\nSample class changes:")
    print(f"{'Class':<5} {'Name':<25} {'Original':<10} {'Balanced':<10} {'Change':<10}")
    print(f"{'-'*70}")
    
    for class_id in sorted(set(list(original_counts.keys())[:10])):
        class_name = class_names[class_id] if class_id < len(class_names) else f"Class_{class_id}"
        orig_count = original_counts.get(class_id, 0)
        bal_count = balanced_counts.get(class_id, 0)
        change = ((bal_count - orig_count) / orig_count * 100) if orig_count > 0 else 0
        
        display_name = class_name[:24] if len(class_name) > 24 else class_name
        print(f"{class_id:<5} {display_name:<25} {orig_count:<10} {bal_count:<10} {change:+.1f}%")

def main():
    analyze_balanced_dataset()
    analyze_original_vs_balanced()

if __name__ == "__main__":
    main()
